package com.ebon.energy.fms.controller;

import com.azure.core.annotation.Get;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.domain.po.SiteInverterAddPO;
import com.ebon.energy.fms.domain.po.SiteInverterUpdatePO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.BatteryStatusDto;
import com.ebon.energy.fms.domain.vo.site.EnergyFlowDto;
import com.ebon.energy.fms.domain.vo.site.RenewableGaugeDto;
import com.ebon.energy.fms.domain.vo.site.SiteHistoryVO;
import com.ebon.energy.fms.service.SiteDashboardHistoryService;
import com.ebon.energy.fms.service.SiteService;
import com.ebon.energy.fms.service.site.SiteEnergyService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 站点管理
 */
@Slf4j
@RestController
@RequestMapping("/api/site")
public class SiteController {

    @Resource
    private SiteService siteService;

    @Resource
    private SiteEnergyService siteEnergyService;

    @Resource
    private SiteDashboardHistoryService siteDashboardHistoryService;

    /**
     * 站点列表查询
     *
     * @param po
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<SiteVO>> list(SiteListPO po) {
        return JsonResult.buildSuccess(siteService.getSiteList(po));
    }

    /**
     * 站点详情
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/detail")
    public JsonResult<SiteVO> detail(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteService.getSiteDetail(siteId));
    }

    /**
     * 站点逆变器列表
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/inverter-list")
    public JsonResult<List<SiteInverterInfoVO>> siteInverterList(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteService.getSiteInverterList(siteId));
    }

    /**
     * 站点逆变器更新
     *
     * @param updatePO
     * @return
     */
    @OperateLogAnnotation(name = "Update Site Inverter")
    @PostMapping("/inverter-update")
    public JsonResult inverterUpdate(@Valid @RequestBody SiteInverterUpdatePO updatePO) {
        siteService.updateInverterSelectedRole(updatePO);
        return JsonResult.buildSuccess();
    }

    /**
     * 站点逆变器增加
     *
     * @param addPO
     * @return
     */
    @OperateLogAnnotation(name = "Add Site Inverter")
    @PostMapping("/inverter-add")
    public JsonResult inverterAdd(@Valid @RequestBody SiteInverterAddPO addPO) {
        siteService.addInverter(addPO);
        return JsonResult.buildSuccess();
    }

    /**
     * 站点逆变器角色列表
     *
     * @return
     */
    @GetMapping("/phase-role/list")
    public JsonResult<List<PhaseRoleVO>> phaseRoles() {
        return JsonResult.buildSuccess(PhaseRole.getPhaseRoles().stream()
                .filter(e -> !e.getValue().equals(PhaseRole.Standalone.name())).collect(Collectors.toList()));
    }

    /**
     * 站点历史电量统计
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/history")
    public JsonResult<SiteHistoryVO> siteHistory(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteDashboardHistoryService.getSiteHistory(siteId, 7));
    }

    @GetMapping("/{id}/energyflow")
    public JsonResult<EnergyFlowDto> energyflow(@PathVariable("id") String siteId) {
        return JsonResult.buildSuccess( siteEnergyService.getEnergyFlow(siteId));
    }

    @GetMapping("/{id}/batterywidget")
    public JsonResult<BatteryStatusDto> batteryWidget(@PathVariable("id") String siteId) {
        return JsonResult.buildSuccess(siteEnergyService.getBatteryStatus(siteId));
    }

    @GetMapping("/{id}/renewablegauge")
    public JsonResult<RenewableGaugeDto> renewableGauge(@PathVariable("id") String siteId) {
        return JsonResult.buildSuccess(siteEnergyService.getRenewableGauge(siteId));
    }

}
