// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.common.utils;



import com.ebon.energy.fms.domain.vo.EnergyFlowExtVO;
import com.ebon.energy.fms.domain.vo.EnergyFlowExtendedVO;
import com.ebon.energy.fms.domain.vo.product.control.HardwareFirmwareSpecification;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.util.DateTimeHelper;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;

public class Helper {
    public static final int ACCEPTABLE_IMBALANCE_WH = 100; // WattH
    public static final BigDecimal TO_PERCENT = new BigDecimal("100");

//    public static AppDashboardSiteHistoryDto getDashboardSiteHistoryFromTotalsDto(
//            AllAboutSiteOverview allAboutSite,
//            List<TotalsDto> totals,
//            int days) {
//        if (totals == null) {
//            return null;
//        }
//
//        final int todaysPartialData = 1;
//        int twiceDaysPlus = (days * 2) + todaysPartialData;
//
//        var siteTotals = getDailySiteTotalsDtoFromTotalsDto(allAboutSite, totals, twiceDaysPlus);
//        var decendingDateOrderMerged = siteTotals.totals.stream()
//                .sorted((t1, t2) -> t2.getDate().compareTo(t1.getDate()))
//                .collect(Collectors.toList());
//        var todaysDateInLocalTime = siteTotals.lastDay;
//
//        // Partial to include today so far, plus full week.
//        var thisWeekWithToday = decendingDateOrderMerged.stream()
//                .limit(days + todaysPartialData)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//        // Full week to ignore today, and include first 7 full days.
//        var thisWeekCompleteDays = decendingDateOrderMerged.stream()
//                .skip(todaysPartialData)
//                .limit(days)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//        // Full week prior to this week (excluding today).
//        var weekBeforeCompleteDays = decendingDateOrderMerged.stream()
//                .skip(todaysPartialData + days)
//                .limit(days)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//
//        var powerUsage = new PowerUsageHistoryDto(
//                thisWeekWithToday.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyUsage().getWh(), x.getDailyUsage().isAccurate()))
//                        .collect(Collectors.toList()));
//        var powerUsageWeekBefore = new PowerUsageHistoryDto(
//                weekBeforeCompleteDays.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyUsage().getWh(), x.getDailyUsage().isAccurate()))
//                        .collect(Collectors.toList()));
//
//        var solarProduction = new PowerUsageHistoryDto(
//                thisWeekWithToday.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyGeneration().getWh(), x.getDailyGeneration().isAccurate()))
//                        .collect(Collectors.toList()));
//        var solarProductionWeekBefore = new PowerUsageHistoryDto(
//                weekBeforeCompleteDays.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyGeneration().getWh(), x.getDailyGeneration().isAccurate()))
//                        .collect(Collectors.toList()));
//
//        var bought = new PowerUsageHistoryDto(
//                thisWeekWithToday.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyBought().getWh(), x.getDailyBought().isAccurate()))
//                        .collect(Collectors.toList()));
//        var boughtWeekBefore = new PowerUsageHistoryDto(
//                weekBeforeCompleteDays.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailyBought().getWh(), x.getDailyBought().isAccurate()))
//                        .collect(Collectors.toList()));
//
//        var sold = new PowerUsageHistoryDto(
//                thisWeekWithToday.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailySold().getWh(), x.getDailySold().isAccurate()))
//                        .collect(Collectors.toList()));
//        var soldWeekBefore = new PowerUsageHistoryDto(
//                weekBeforeCompleteDays.stream()
//                        .map(x -> new EnergyLabelDto(x.getDate(), getLabel(x.getDate(), todaysDateInLocalTime),
//                                x.getDailySold().getWh(), x.getDailySold().isAccurate()))
//                        .collect(Collectors.toList()));
//
//        return new AppDashboardSiteHistoryDto(
//                powerUsage,
//                powerUsageWeekBefore,
//                solarProduction,
//                solarProductionWeekBefore,
//                bought,
//                boughtWeekBefore,
//                sold,
//                soldWeekBefore);
//    }
//
//    private static String getLabel(LocalDate date, LocalDate todaysDateInLocalTime) {
//        return getDayOfTheWeekOrToday(date, todaysDateInLocalTime);
//    }

//    /**
//     * Returns a descending ordered list of DailySiteTotalsDto.
//     * @param allAboutSite allAboutSite
//     * @param totals totals
//     * @param days days
//     * @return Totals and LastDay
//     */
//    public static SiteTotalsResult getDailySiteTotalsDtoFromTotalsDto(
//            AllAboutSiteOverview allAboutSite,
//            List<TotalsDto> totals,
//            int days) {
//
//        var potentiallyIncompleteDailyTotalsRaw = totals.stream()
//                .map(x -> x.asPublicDto())
//                .collect(Collectors.toList());
//
//        var potentiallyIncompleteDailyTotals = potentiallyIncompleteDailyTotalsRaw.stream()
//                .map(history -> {
//                    var supportsLoadContributors = allAboutSite.getSupportsLoadContributorsSince() != null &&
//                            !history.getDate().isBefore(allAboutSite.getSupportsLoadContributorsSince());
//
//                    var energies = EnergyBalancer.getEnergies(new EnergyBalancingInputs(
//                            history.getDailyUsage().getWh(),
//                            history.getDailySold().getWh(),
//                            history.getDailyBought().getWh(),
//                            history.getDailyGeneration().getWh(),
//                            history.getDailyBatteryCharged() != null ? history.getDailyBatteryCharged().getWh() : null,
//                            history.getDailyBatteryDischarged() != null ? history.getDailyBatteryDischarged().getWh() : null,
//                            allAboutSite.getMaximumPossiblePVOutput(),
//                            allAboutSite.getDeviceDetails().get(0).getFirstSiteDeviceDto().getEnergyBalancingMethod(),
//                            ACCEPTABLE_IMBALANCE_WH * allAboutSite.getDeviceDetails().size(),
//                            supportsLoadContributors));
//
//                    EnergyVal dailyBatteryCharged = energies.getBatteryChargedWh() != null
//                            ? new EnergyVal(energies.getBatteryChargedWh().asInt(),
//                                    history.getDailyBatteryCharged() != null ? history.getDailyBatteryCharged().isAccurate() : false)
//                            : null;
//                    EnergyVal dailyBatteryDischarged = energies.getBatteryDischargedWh() != null
//                            ? new EnergyVal(energies.getBatteryDischargedWh().asInt(),
//                                    history.getDailyBatteryDischarged() != null ? history.getDailyBatteryDischarged().isAccurate() : false)
//                            : null;
//
//                    return new DailySiteTotalsDto(
//                            history.getDate(),
//                            new EnergyVal(energies.getUsageWh().asInt(), history.getDailyUsage().isAccurate()),
//                            new EnergyVal(energies.getSoldWh().asInt(), history.getDailySold().isAccurate()),
//                            new EnergyVal(energies.getBoughtWh().asInt(), history.getDailyBought().isAccurate()),
//                            new EnergyVal(energies.getGenerationWh() != null ? energies.getGenerationWh().asInt() : 0,
//                                    history.getDailyGeneration().isAccurate()),
//                            dailyBatteryCharged,
//                            dailyBatteryDischarged);
//                })
//                .collect(Collectors.toList());
//
//        var completeDecendingDateOrderDailyTotalEmptySlots = IntStream.range(-(days - 1), 1)
//                .mapToObj(day -> DailySiteTotalsDto.empty(allAboutSite.getTodaysDateInLocalTime().plusDays(day)))
//                .sorted((t1, t2) -> t2.getDate().compareTo(t1.getDate()))
//                .collect(Collectors.toList());
//
//        var decendingDateOrderMerged = getMergedTotals(potentiallyIncompleteDailyTotals, completeDecendingDateOrderDailyTotalEmptySlots)
//                .collect(Collectors.toList());
//
//        return new SiteTotalsResult(decendingDateOrderMerged, allAboutSite.getTodaysDateInLocalTime());
//    }
//
//    private static Stream<DailySiteTotalsDto> getMergedTotals(
//            List<DailySiteTotalsDto> potentiallyIncompleteDailyTotals,
//            List<DailySiteTotalsDto> slots) {
//
//        var potentiallyIncompleteDailyTotalsMap = potentiallyIncompleteDailyTotals.stream()
//                .collect(Collectors.toMap(DailySiteTotalsDto::getDate, x -> x));
//
//        return slots.stream().map(slot -> {
//            DailySiteTotalsDto total = potentiallyIncompleteDailyTotalsMap.get(slot.getDate());
//            return total != null ? total : slot;
//        });
//    }
//
//    public static String getDayOfTheWeekOrToday(LocalDate date, LocalDate specialDate) {
//        if (specialDate.equals(date)) {
//            return "Today";
//        }
//
//        switch (date.getDayOfWeek()) {
//            case MONDAY: return "Mon";
//            case TUESDAY: return "Tue";
//            case WEDNESDAY: return "Wed";
//            case THURSDAY: return "Thu";
//            case FRIDAY: return "Fri";
//            case SATURDAY: return "Sat";
//            case SUNDAY: return "Sun";
//            default: throw new RuntimeException();
//        }
//    }
//
//    public static SystemHistoryDto getSiteSystemHistoryFromTotalsDto(
//            AllAboutSiteOverview allAboutSite,
//            List<TotalsDto> totals,
//            int days) {
//        if (totals == null) {
//            return null;
//        }
//
//        final int todaysPartialData = 1;
//        int twiceDaysPlus = (days * 2) + todaysPartialData;
//
//        var siteTotals = getDailySiteTotalsDtoFromTotalsDto(allAboutSite, totals, twiceDaysPlus);
//        var decendingDateOrderMerged = siteTotals.totals.stream()
//                .sorted((t1, t2) -> t2.getDate().compareTo(t1.getDate()))
//                .collect(Collectors.toList());
//        var todaysDateInLocalTime = siteTotals.lastDay;
//
//        // Partial to include today so far, plus full week.
//        var thisWeekWithToday = decendingDateOrderMerged.stream()
//                .limit(days + todaysPartialData)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//        // Full week to ignore today, and include first 7 full days.
//        var thisWeekCompleteDays = decendingDateOrderMerged.stream()
//                .skip(todaysPartialData)
//                .limit(days)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//        // Full week prior to this week (excluding today).
//        var weekBeforeCompleteDays = decendingDateOrderMerged.stream()
//                .skip(todaysPartialData + days)
//                .limit(days)
//                .sorted((t1, t2) -> t1.getDate().compareTo(t2.getDate()))
//                .collect(Collectors.toList());
//
//        // Section: ******** & ******** - Don't display energy usage when batteries have been dropped, as the site balancing will be out.
//        // https://redbacktech.sharepoint.com/:w:/g/EZbwzc8old1Lm5LpnqgcQewBJwbwH2D6mU6n81G5Oxs4VA?e=bKouTB
//        SevenDayHistoryDto powerUsage = null;
//        if (!allAboutSite.isHasBatteries() ||
//                (allAboutSite.getDataForDashboard() != null &&
//                 allAboutSite.getDataForDashboard().getBatterySummary() != null &&
//                 allAboutSite.getDataForDashboard().getBatterySummary().getStatus() != BatteryStatusValue.DISABLED &&
//                 allAboutSite.getDataForDashboard().getBatterySummary().getStatus() != BatteryStatusValue.DISCONNECTED)) {
//
//            powerUsage = getSevenDayHistoryFromDailySiteTotalEnergyVals(
//                    allAboutSite.getPublicSiteId(),
//                    thisWeekWithToday.stream()
//                            .map(t -> new DayPowerPairDto(getLabel(t.getDate(), todaysDateInLocalTime),
//                                    t.getDailyUsage().getWh().asInt(), t.getDailyUsage().isAccurate()))
//                            .collect(Collectors.toList()),
//                    thisWeekCompleteDays.stream().map(v -> v.getDailyUsage()).collect(Collectors.toList()),
//                    weekBeforeCompleteDays.stream().map(v -> v.getDailyUsage()).collect(Collectors.toList()));
//        }
//
//        var solarProduction = getSevenDayHistoryFromDailySiteTotalEnergyVals(
//                allAboutSite.getPublicSiteId(),
//                thisWeekWithToday.stream()
//                        .map(t -> new DayPowerPairDto(getLabel(t.getDate(), todaysDateInLocalTime),
//                                t.getDailyGeneration().getWh().asInt(), t.getDailyGeneration().isAccurate()))
//                        .collect(Collectors.toList()),
//                thisWeekCompleteDays.stream().map(v -> v.getDailyGeneration()).collect(Collectors.toList()),
//                weekBeforeCompleteDays.stream().map(v -> v.getDailyGeneration()).collect(Collectors.toList()));
//
//        var bought = getSevenDayHistoryFromDailySiteTotalEnergyVals(
//                allAboutSite.getPublicSiteId(),
//                thisWeekWithToday.stream()
//                        .map(t -> new DayPowerPairDto(getLabel(t.getDate(), todaysDateInLocalTime),
//                                t.getDailyBought().getWh().asInt(), t.getDailyBought().isAccurate()))
//                        .collect(Collectors.toList()),
//                thisWeekCompleteDays.stream().map(v -> v.getDailyBought()).collect(Collectors.toList()),
//                weekBeforeCompleteDays.stream().map(v -> v.getDailyBought()).collect(Collectors.toList()));
//
//        var sold = getSevenDayHistoryFromDailySiteTotalEnergyVals(
//                allAboutSite.getPublicSiteId(),
//                thisWeekWithToday.stream()
//                        .map(t -> new DayPowerPairDto(getLabel(t.getDate(), todaysDateInLocalTime),
//                                t.getDailySold().getWh().asInt(), t.getDailySold().isAccurate()))
//                        .collect(Collectors.toList()),
//                thisWeekCompleteDays.stream().map(v -> v.getDailySold()).collect(Collectors.toList()),
//                weekBeforeCompleteDays.stream().map(v -> v.getDailySold()).collect(Collectors.toList()));
//
//        // Put it all together.
//        var boughtSold = new BoughtSoldDto(
//                allAboutSite.getPublicSiteId(),
//                bought,
//                sold);
//
//        var siteHistory = new SystemHistoryDto(
//                powerUsage,
//                solarProduction,
//                boughtSold,
//                allAboutSite.isAcCoupledMode());
//
//        return siteHistory;
//    }
//
//    public static SevenDayHistoryDto getSevenDayHistoryFromDailySiteTotalEnergyVals(
//            String publicSiteId,
//            List<DayPowerPairDto> thisWeekWithToday,
//            List<EnergyVal> thisWeekCompleteDays,
//            List<EnergyVal> weekBeforeCompleteDays) {
//
//        // This feature has been enabled in production for some time, so just leaving it on for 2.16 onwards.
//        boolean applyChartSafeDataFeature = true; // new DashboardSafeDataFeature(loggerService).FeatureEnabled;
//
//        var thisWeekUsageSumWh = thisWeekCompleteDays.stream()
//                .map(v -> v.getWh().asDecimal())
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        var priorWeekUsageSumWh = weekBeforeCompleteDays.stream()
//                .map(x -> x.getWh().asDecimal())
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        BigDecimal weekDiffUsageNegativeIsLess = BigDecimal.ZERO;
//        if (priorWeekUsageSumWh.compareTo(BigDecimal.ZERO) > 0) {
//            weekDiffUsageNegativeIsLess = thisWeekUsageSumWh.subtract(priorWeekUsageSumWh)
//                    .divide(priorWeekUsageSumWh, 10, BigDecimal.ROUND_HALF_UP)
//                    .multiply(TO_PERCENT);
//        }
//
//        var sevenDayHistory = new SevenDayHistoryDto(
//                publicSiteId,
//                thisWeekUsageSumWh,
//                weekDiffUsageNegativeIsLess,
//                thisWeekWithToday,
//                applyChartSafeDataFeature);
//
//        return sevenDayHistory;
//    }

    public static EnergyFlowDto getEnergyFlowFromParts(
            String flowId,
            EnergyFlowExtendedVO flowExtended,
            List<HardwareFirmwareSpecification> specifications,
            Integer siteExportLimitW,
            Integer globalCurrentGridExportLimit) {
        
        if (flowExtended == null) {
            return null;
        }

        var flow = flowExtended.getFlow();

        var cosmicPowers = new ArrayList<SolarSourceDto>();
        if (flow.getPVW() != null) {
            cosmicPowers.add(new SolarSourceDto("Redback System", flow.getPVW().asInt()));
        }

        if (flow.getThirdPartyW() != null) {
            cosmicPowers.add(new SolarSourceDto("Other System", flow.getThirdPartyW().asInt()));
        }

        // This feature has been enabled in production for some time, so just leaving it on for 2.16 onwards.
        var dashboardSafeDataFeatureEnabled = true; // new DashboardSafeDataFeature(loggerService).FeatureEnabled;

        // Product requirements - https://dev.azure.com/rbtech/Redback/_workitems/edit/66265
        //                        https://dev.azure.com/rbtech/Redback/_wiki/wikis/Redback.wiki/3265
        // Site requirements    - https://redbacktech.sharepoint.com/:w:/g/EZbwzc8old1Lm5LpnqgcQewBJwbwH2D6mU6n81G5Oxs4VA?e=bKouTB
        var displayExportWarning = false;
        if (siteExportLimitW != null) {
            var comparableValue = siteExportLimitW - (globalCurrentGridExportLimit != null ? globalCurrentGridExportLimit : 0);

            if (flowExtended.getInput().getGridNegativeIsImportW().getValue().compareTo(BigDecimal.valueOf(comparableValue)) > 0) {
                displayExportWarning = true;
            }
        }

        EnergyFlowDto energyFlow = new EnergyFlowDto(
                flowId,
                SiteOverviewHelper.isOnline(flow.getAtUtc().toInstant()),
                flow.getHasBatteries(),
                EnergyFlowExtVO.getBatteryStatusString(flow),
                flow.getAtUtc().toInstant(),
                DateTimeHelper.redbackDateFormat(flow.getAtUtc()),
                new SolarSourcesSummaryDto(cosmicPowers),
                flow.getBatteryNegativeIsChargingW().asInt(),
                (int)Math.abs(flow.getGridNegativeIsImportW().asInt()),
                EnergyFlowExtVO.getGridStatusString(flow.getGridStatus()),
                new HouseSummaryDto((flow.getACLoadW().add(flow.getBackupLoadW())).asInt(), flow.getBackupLoadW().asInt()),
                dashboardSafeDataFeatureEnabled,

                // TODO: Remove this, should be replaced with separate HasGridTieInverter & ShowBatterWidget properties.
                // As of <= 2.16 IsGridTieInverter seems to be tied with displaying the battery widget.
                specifications.stream().allMatch(s -> s != null && s.isGridTieInverter()),

                displayExportWarning,
                flow.getDRM0Enable()
        );

        return energyFlow;
    }

    // got it from Cloud.Entities.ProductUtilities that is old 4.6
    public static boolean isOnline(LocalDateTime latestTimeReceived) {
        return latestTimeReceived != null && 
               Duration.between(latestTimeReceived, LocalDateTime.now()).toMinutes() <= 10;
    }
//
//    public static AllAboutDevice getDeviceDetails(
//            SiteDeviceDto device,
//            String loggedInUserId,
//            ISpecificationRepository specificationRepository,
//            IProductDbRepository productDbRepository,
//            IClock clock,
//            ITelemetryRepository telemetryRepository) {
//
//        var specification = specificationRepository.getInstallationSpec(loggedInUserId, device.getSerialNumber());
//        var deviceInfoAndSettings = specificationRepository.getDeviceInfoAndSettings(loggedInUserId, device.getSerialNumber());
//        var maximumPossiblePVOutputWh = DataServiceHelper.getDeviceMaximumPossiblePVOutputWh(productDbRepository, device.getSerialNumber());
//
//        GenericInverterConfiguration conf = null;
//        BatteryBackUpDto batteryBackUp = null;
//
//        var systemStatus = device.getLatestSystemStatus() != null
//                ? Conversion.convertStatus(device.getLatestSystemStatus())
//                : null;
//
//        boolean isRoss1 = systemStatus == null
//                || systemStatus.getOuijaBoard() == null
//                || systemStatus.getOuijaBoard().getSoftwareVersion() == null
//                || new RossVersion(systemStatus.getOuijaBoard().getSoftwareVersion()).isRoss1();
//
//        boolean isBatteryMismatchProtectionEnabled = false;
//
//        if (systemStatus != null) {
//            // This could/should be folded into GetInstallationSpec
//            if (isRoss1) {
//                var electrical = productDbRepository.getElectricalConfigurationRoss1(loggedInUserId, device.getSerialNumber());
//                conf = UnifiedElectricalSettingsHelper.fromElectricalConfiguration(electrical).getConf();
//                isBatteryMismatchProtectionEnabled = false;
//            } else if (deviceInfoAndSettings != null && deviceInfoAndSettings.getDesired() != null) {
//                var reader = SettingsReaderProvider.get(deviceInfoAndSettings);
//                conf = GenericInverterConfiguration.fromSettings(reader);
//
//                isBatteryMismatchProtectionEnabled = reader.isBatteryMismatchProtectionEnabled();
//            }
//        }
//
//        var todaysDateInLocalTime = SiteRepository.getLocalDateFromBclTimeZone(clock.getCurrentInstant(), device.getBclTimeZoneId());
//
//        var supportsLoadContributorsSince = device.getSupportsLoadContributors();
//
//        // TODO: Can we get this in a separate API call, to reduce load times?
//        batteryBackUp = computeHoursOnBackup(device.getSerialNumber(), conf, systemStatus, specification, clock, telemetryRepository);
//
//        WattHour availableBatteryEnergyOverMinOffgridSoCWh = new WattHour(0);
//        if (systemStatus != null) {
//            var snapshot = Gen2SiteSnapshot.fromStatus(systemStatus);
//
//            var batteryUtility = new BatteryEnergyUtility(snapshot, conf);
//
//            availableBatteryEnergyOverMinOffgridSoCWh = BatteryEnergyStatics
//                    .getUsableEnergyWh(batteryUtility.estimateCurrentlyAccessibleEnergyInBatteryWh(true));
//        }
//
//        var deviceDataForDashboard = DataForDashboard.make(systemStatus);
//
//        return new AllAboutDevice(
//                device.getSerialNumber(),
//                deviceDataForDashboard,
//                device,
//                specification,
//                conf,
//                todaysDateInLocalTime,
//                specification != null ? specification.isHasSolar() : false,
//                (conf != null ? conf.isHasBatteries() : false)
//                        // To match the product dashboard (mostly just effects SIMs) upscale battery flow values to HasBatteries = true.
//                        || (deviceDataForDashboard != null &&
//                            deviceDataForDashboard.getBatterySummary() != null &&
//                            deviceDataForDashboard.getBatterySummary().getP() != null),
//                maximumPossiblePVOutputWh,
//                supportsLoadContributorsSince,
//                isBatteryMismatchProtectionEnabled,
//                availableBatteryEnergyOverMinOffgridSoCWh,
//                batteryBackUp);
//    }
//
//    public static BatteryBackUpDto computeHoursOnBackup(
//            String serialNumber,
//            GenericInverterConfiguration config,
//            SystemStatus systemStatus,
//            HardwareFirmwareSpecification specification,
//            IClock clock,
//            ITelemetryRepository telemetryRepository) {
//
//        if (config == null || systemStatus == null || !config.isHasBatteries()) {
//            return new BatteryBackUpDto(
//                    null,
//                    BackUpSupportStatus.NO_BATTERIES,
//                    specification != null ? specification.isSupportsConnectedPV() : false,
//                    serialNumber);
//        }
//
//        var snapshot = Gen2SiteSnapshot.fromStatus(systemStatus);
//
//        var batteryUtility = new BatteryEnergyUtility(snapshot, config);
//
//        var availableBatteryEnergyOverMinSoCWh = BatteryEnergyStatics
//                .getUsableEnergyWh(batteryUtility.estimateCurrentlyAccessibleEnergyInBatteryWh(true));
//
//        var fromOneHourAgo = clock.getCurrentInstant().minus(Duration.ofHours(1));
//
//        var toNow = clock.getCurrentInstant();
//
//        var lastHourOfSystemStatuses = telemetryRepository.getDeviceAverageBackupAndSolarPowerBetween(
//                serialNumber,
//                fromOneHourAgo,
//                toNow);
//
//        if (lastHourOfSystemStatuses != null) {
//            var averageBackupPowerOverLastHourInWatts = lastHourOfSystemStatuses.getAverageBackupP();
//
//            // Disregard backup load less than 40 watts since inverter/ouija could be the only loads on the backup
//            // Fixes 40456
//            Watt nonTrivialbackupPowerThreshold = new Watt(40);
//
//            if (Math.abs(averageBackupPowerOverLastHourInWatts.getValue()) > nonTrivialbackupPowerThreshold.getValue()) {
//                Watt averageSolarPower = new Watt(0);
//                if (specification != null && specification.isSupportsConnectedPV()) {
//                    averageSolarPower = lastHourOfSystemStatuses.getAverageSolarP();
//                }
//
//                var powerBatteryWouldNeedToSupplyAsDecimal = averageBackupPowerOverLastHourInWatts.asDecimal()
//                        .subtract(averageSolarPower.asDecimal());
//
//                if (powerBatteryWouldNeedToSupplyAsDecimal.compareTo(BigDecimal.ZERO) > 0) {
//                    var hoursOfBackupPower = availableBatteryEnergyOverMinSoCWh.asDecimal()
//                            .divide(powerBatteryWouldNeedToSupplyAsDecimal, 10, BigDecimal.ROUND_HALF_UP);
//
//                    var backUpSupport = averageSolarPower.asDecimal().compareTo(BigDecimal.ZERO) > 0
//                            ? BackUpSupportStatus.PV_AND_BATTERY_MEETING_BACKUP_LOAD
//                            : BackUpSupportStatus.BATTERY_MEETING_BACKUP_LOAD;
//
//                    return new BatteryBackUpDto(
//                            hoursOfBackupPower.doubleValue(),
//                            backUpSupport,
//                            specification != null ? specification.isSupportsConnectedPV() : false,
//                            serialNumber);
//                } else {
//                    return new BatteryBackUpDto(
//                            null,
//                            BackUpSupportStatus.PV_MEETING_BACKUP_LOAD,
//                            specification != null ? specification.isSupportsConnectedPV() : false,
//                            serialNumber);
//                }
//            }
//        }
//
//        return new BatteryBackUpDto(
//                0.0,
//                BackUpSupportStatus.NO_BACKUP,
//                specification != null ? specification.isSupportsConnectedPV() : false,
//                serialNumber);
//    }
//
//    public static List<SiteProduct> buildSiteProducts(List<SiteProductInformation> products, List<BatteryInverterInformationDto> batteryInverterInfos) {
//        boolean isSiteOnline = true;
//
//        var result = new ArrayList<SiteProduct>();
//        result.add(new SiteProduct(
//                products.get(0).getSiteId(),
//                SiteHelper.SITE_CAPTION,
//                false,
//                "",
//                "",
//                false,
//                "",
//                "",
//                ""));
//
//        result.addAll(
//                products.stream()
//                        .map(p -> buildSiteProductsList(p.getProduct(), p.getProductFriendlyName(),
//                                p.getLastSystemStatusReceived(), batteryInverterInfos, isSiteOnline))
//                        .collect(Collectors.toList())
//        );
//
//        result.stream()
//                .filter(p -> p.getName().toUpperCase().equals(SiteHelper.SITE_CAPTION.toUpperCase()))
//                .findFirst()
//                .ifPresent(p -> p.setOnline(isSiteOnline));
//
//        return result;
//    }
//
//    public static List<SiteProduct> buildSiteProducts(List<ProductTuple> products, List<BatteryInverterInformationDto> batteryInfos) {
//        boolean isSiteOnline = true;
//
//        var result = new ArrayList<SiteProduct>();
//        result.add(new SiteProduct(
//                products.get(0).getSiteId(),
//                SiteHelper.SITE_CAPTION,
//                false,
//                "",
//                "",
//                false,
//                "",
//                "",
//                ""));
//
//        result.addAll(
//                products.stream()
//                        .map(p -> buildSiteProductsList(p.getSerialNumber(), p.getProductFriendlyName(),
//                                p.getLastSystemStatusReceived(), batteryInfos, isSiteOnline))
//                        .collect(Collectors.toList())
//        );
//
//        result.stream()
//                .filter(p -> p.getName().toUpperCase().equals(SiteHelper.SITE_CAPTION.toUpperCase()))
//                .findFirst()
//                .ifPresent(p -> p.setOnline(isSiteOnline));
//
//        return result;
//    }
//
//    private static SiteProduct buildSiteProductsList(String serialNumber, String productFriendlyName,
//            LocalDateTime lastSystemStatusReceived, List<BatteryInverterInformationDto> batteryInverterInfos,
//            boolean isSiteOnline) {
//
//        var isOnline = Helper.isOnline(lastSystemStatusReceived);
//
//        if (!isOnline) {
//            isSiteOnline = false;
//        }
//
//        var batteryInfo = batteryInverterInfos.stream()
//                .filter(pp -> pp.getProductSerialNumber().equals(serialNumber))
//                .findFirst()
//                .orElse(null);
//
//        return new SiteProduct(
//                serialNumber,
//                serialNumber,
//                false,
//                batteryInfo != null ? batteryInfo.getInverterModel() : "",
//                batteryInfo != null ? batteryInfo.getBatteryCapacity() : "",
//                isOnline,
//                productFriendlyName,
//                batteryInfo != null ? batteryInfo.getPvCapacity() : "",
//                batteryInfo != null ? batteryInfo.getDisplayModelName() : "");
//    }

}
