package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.Tuple4;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class SiteOverviewHelper {

    // 判断设备是否在线（最后更新时间在10分钟内）
    public static boolean isOnline(Instant dateTimeInstant) {
        return Optional.ofNullable(dateTimeInstant)
                .map(lastUpdate -> {
                    Instant now = Instant.now(); // 当前UTC时间
                    Duration duration = Duration.between(lastUpdate, now);
                    return duration.toMinutes() <= 10;
                })
                .orElse(false); // 时间为null时返回false
    }

    // 判断设备是否离线（与isOnline相反）
    public static boolean isOffline(Instant dateTimeInstant) {
        return !isOnline(dateTimeInstant);
    }
    
    public static boolean isOffline(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        
        return isOffline(dateTime.toInstant());
    }

    public static Tuple2<BatteryStatus, Boolean> combineBatteryStatuses(List<BatteryStatus> batteryStatuses) {
        // 处理空列表
        if (batteryStatuses == null || batteryStatuses.isEmpty()) {
            BatteryStatus batteryStatus = new BatteryStatus();
            batteryStatus.setStatus(BatteryStatusValue.Disconnected);
            return new Tuple2<>(
                    batteryStatus,
                    false
            );
        }

        // 提取所有非空的BatteryStatus
        List<BatteryStatus> validStatuses = batteryStatuses.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 组合电压（V）：取最大值
        Double combinedV = validStatuses.stream()
                .map(BatteryStatus::getV)
                .filter(Objects::nonNull)
                .reduce(Double::max)
                .orElse(null);

        // 组合功率（P）：求和
        Double combinedP = validStatuses.stream()
                .map(BatteryStatus::getP)
                .filter(Objects::nonNull)
                .reduce(Double::sum)
                .orElse(null);

        // 组合容量（CapacityAh）
        Double combinedCapacityAh = null;
        if (!validStatuses.stream().allMatch(b -> b.getCapacity() == null)) {
            combinedCapacityAh = 0.0;
            for (BatteryStatus battery : validStatuses) {
                Double capacity = battery.getCapacity();
                Double voltage = battery.getV();
                if (capacity != null && capacity > 0 && voltage != null && voltage > 0 && combinedV != null && combinedV > 0) {
                    combinedCapacityAh += (capacity * voltage) / combinedV;
                }
            }
            combinedCapacityAh = combinedCapacityAh == 0 ? null : combinedCapacityAh; // 若总和为0则设为null
        }

        // 组合额定容量（RatedCapacityWh）：求和
        Double combinedRatedCapacityWh = validStatuses.stream()
                .map(BatteryStatus::getRatedCapacity)
                .filter(Objects::nonNull)
                .reduce(Double::sum)
                .orElse(null);

        // 组合剩余容量（RemainingCapacityWh）：求和
        Double combinedRemainingCapacityWh = validStatuses.stream()
                .map(BatteryStatus::getRemainingCapacity)
                .filter(Objects::nonNull)
                .reduce(Double::sum)
                .orElse(null);

        // 组合SoC（通过RatedCapacity和SoC计算）
        Double combinedSoC0to100 = null;
        if (combinedRatedCapacityWh != null && combinedRatedCapacityWh > 0) {
            Double combinedRemainingBySoc = validStatuses.stream()
                    .map(b -> {
                        Double soc = b.getSoC();
                        Double rated = b.getRatedCapacity();
                        return soc != null && rated != null ? (soc / 100) * rated : null;
                    })
                    .filter(Objects::nonNull)
                    .reduce(Double::sum)
                    .orElse(null);

            if (combinedRemainingBySoc != null) {
                combinedSoC0to100 = (combinedRemainingBySoc / combinedRatedCapacityWh) * 100;
                combinedSoC0to100 = Math.max(0, Math.min(100, combinedSoC0to100)); // 限制在0-100
            }
        }

        // 组合日输入/输出能量
        Double combinedDayTotalInputE = validStatuses.stream()
                .map(BatteryStatus::getDayTotalInputE)
                .filter(Objects::nonNull)
                .reduce(Double::sum)
                .orElse(null);

        Double combinedDayTotalOutputE = validStatuses.stream()
                .map(BatteryStatus::getDayTotalOutputE)
                .filter(Objects::nonNull)
                .reduce(Double::sum)
                .orElse(null);

        // 组合不匹配时间（取最大值）
        Integer mismatchedTimeMinutes = validStatuses.stream()
                .map(BatteryStatus::getMismatchedTimeMinutes)
                .filter(Objects::nonNull)
                .reduce(Integer::max)
                .orElse(null);

        // 组合状态（BatteryStatusValue）
        BatteryStatusValue combinedStatus = BatteryStatusValue.Disconnected;
        if (!validStatuses.isEmpty()) {
            // 检查所有状态是否一致
            Optional<BatteryStatusValue> firstStatus = validStatuses.stream()
                    .map(BatteryStatus::getStatus)
                    .filter(Objects::nonNull)
                    .findFirst();

            if (firstStatus.isPresent() && validStatuses.stream()
                    .allMatch(b -> b.getStatus() == firstStatus.get())) {
                combinedStatus = firstStatus.get();
            } else {
                // 按规则判断混合状态
                if (validStatuses.stream().anyMatch(b -> b.getStatus() == BatteryStatusValue.Disconnected)) {
                    combinedStatus = BatteryStatusValue.Disconnected;
                } else if (validStatuses.stream().anyMatch(b -> b.getStatus() == BatteryStatusValue.Disabled)) {
                    combinedStatus = BatteryStatusValue.Disabled;
                    combinedP = null;
                    combinedV = null;
                    combinedSoC0to100 = null;
                } else if (combinedP != null && combinedP > 0) {
                    combinedStatus = BatteryStatusValue.Discharging;
                } else if (combinedP != null && combinedP < 0) {
                    combinedStatus = BatteryStatusValue.Charging;
                } else {
                    combinedStatus = BatteryStatusValue.Idle;
                }
            }
        }

        // 检查BMS版本不匹配
        boolean combinedBmsVersionMismatch = false;
        if (!validStatuses.isEmpty() && combinedStatus != BatteryStatusValue.Disabled && combinedStatus != BatteryStatusValue.Disconnected) {
            boolean allValid = validStatuses.stream()
                    .allMatch(b -> (b.getBatteries() == null || b.getBatteries().isEmpty()) || b.getRatedCapacity() != null);
            combinedBmsVersionMismatch = !allValid;
            if (combinedBmsVersionMismatch) {
                combinedSoC0to100 = null;
            }
        }

        // 构建组合后的BatteryStatus
        BatteryStatus combinedBatteryStatus = new BatteryStatus();
        combinedBatteryStatus.setV(combinedV);
        combinedBatteryStatus.setP(combinedP);
        combinedBatteryStatus.setSoC(combinedSoC0to100);
        combinedBatteryStatus.setCapacity(combinedCapacityAh);
        combinedBatteryStatus.setRatedCapacity(combinedRatedCapacityWh);
        combinedBatteryStatus.setRemainingCapacity(combinedRemainingCapacityWh);
        combinedBatteryStatus.setDayTotalInputE(combinedDayTotalInputE);
        combinedBatteryStatus.setDayTotalOutputE(combinedDayTotalOutputE);
        combinedBatteryStatus.setMismatchedTimeMinutes(mismatchedTimeMinutes);
        combinedBatteryStatus.setStatus(combinedStatus);
        combinedBatteryStatus.setBatteries(validStatuses.stream()
                .map(BatteryStatus::getBatteries)
                .filter(Objects::nonNull).flatMap(batteries -> batteries.stream()).collect(Collectors.toList()));

        return new Tuple2(combinedBatteryStatus, combinedBmsVersionMismatch);
    }

    public static Tuple2<Watt, GridStatusValue> combineGridStatusesOnDifferentPhases(List<DataForDashboardVO> gridStatuses) {
        GridStatusValue status = GridStatusValue.Idle;
        BigDecimal p = null;

        // 计算总功率
        if (gridStatuses != null && !gridStatuses.isEmpty()) {
            Optional<BigDecimal> sum = gridStatuses.stream()
                    .filter(Objects::nonNull)
                    .map(DataForDashboardVO::getGridP)
                    .filter(Objects::nonNull)
                    .map(Watt::getValue)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add);
            p = sum.orElse(null);
        }

        // 获取第一个状态并检查是否所有状态一致
        GridStatusValue firstStatus = null;
        if (gridStatuses != null && !gridStatuses.isEmpty()) {
            Optional<GridStatusValue> first = gridStatuses.stream()
                    .filter(Objects::nonNull)
                    .map(DataForDashboardVO::getGridStatus)
                    .filter(Objects::nonNull)
                    .findFirst();
            firstStatus = first.orElse(null);
        }

        final GridStatusValue finalStatus = firstStatus;
        if (firstStatus != null && gridStatuses != null &&
                gridStatuses.stream()
                        .filter(Objects::nonNull)
                        .map(DataForDashboardVO::getGridStatus)
                        .allMatch(s -> s == finalStatus)) {
            status = firstStatus;
        } else {
            // 根据规则计算组合状态
            if (gridStatuses != null &&
                    gridStatuses.stream()
                            .filter(Objects::nonNull)
                            .map(DataForDashboardVO::getGridStatus)
                            .anyMatch(s -> s == GridStatusValue.Disconnected)) {
                status = GridStatusValue.Disconnected;
            } else if (p != null && p.compareTo(BigDecimal.ZERO) > 0) {
                status = GridStatusValue.Export;
            } else if (p != null && p.compareTo(BigDecimal.ZERO) < 0) {
                status = GridStatusValue.Import;
            }
        }

        return new Tuple2<>(new Watt(p), status);
    }

    public static Tuple4<String, String, String, Watt> combineInverterStatuses(List<DataForDashboardVO> inverterStatuses) {
        // 计算总功率，处理空值情况
        BigDecimal totalPower = Optional.ofNullable(inverterStatuses)
                .orElse(List.of())
                .stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getInverterP)
                .filter(Objects::nonNull)
                .map(Watt::getValue)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 返回组合结果（其他字段设为null，功率使用计算结果）
        return new Tuple4(
                null,       // InverterSN
                null,       // ModelName
                null,       // FirmwareVersion
                new Watt(totalPower)  // InverterP
        );
    }

    // 合并同相位电网状态
    public static Tuple2<Watt, GridStatusValue> combineGridStatusesOnSamePhase(List<AllAboutDevice> siteDevices) {
        AllAboutDevice coordinator = siteDevices.stream()
                .filter(d -> d.getPhaseRole() == PhaseRole.Coordinator)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Coordinator device not found"));

        Watt p = coordinator.getDataForDashboard().getGridP();

        GridStatusValue status = siteDevices.stream()
                .anyMatch(d -> d != null
                        && d.getDataForDashboard() != null
                        && d.getDataForDashboard().getGridStatus() == GridStatusValue.Disconnected)
                ? GridStatusValue.Disconnected
                : coordinator.getDataForDashboard().getGridStatus();

        return new Tuple2(p, status);
    }

    // 合并备用负载状态
    public static Tuple2<Watt, Status> combineBackupLoadStatuses(List<DataForDashboardVO> backupLoadStatuses) {
        BigDecimal p = backupLoadStatuses.stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getBackupLoadP)
                .filter(Objects::nonNull)
                .map(Watt::getValue)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Status status = backupLoadStatuses.stream()
                .anyMatch(s -> s != null && s.getBackupLoadStatus() == Status.On)
                ? Status.On
                : backupLoadStatuses.stream()
                .anyMatch(s -> s != null && s.getBackupLoadStatus() == Status.Connected)
                ? Status.Connected
                : Status.Disconnected;

        return new Tuple2(new Watt(p), status);
    }

    // 合并光伏状态
    public static Tuple2<Watt, BigDecimal> combinePVStatuses(List<DataForDashboardVO> pVStatuses) {
        BigDecimal p = pVStatuses.stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getPVP)
                .filter(Objects::nonNull)
                .map(Watt::getValue)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new Tuple2(new Watt(p), null);
    }

    // 合并第三方逆变器状态
    public static Tuple2<Watt, BigDecimal> combineThirdPartyInverterStatuses(
            List<DataForDashboardVO> thirdPartyInverterStatuses) {

        BigDecimal p = thirdPartyInverterStatuses.stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getThirdPartyInverterP)
                .filter(Objects::nonNull)
                .map(Watt::getValue)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new Tuple2(new Watt(p), null);
    }

    // 合并OuijaBoard详情
    public static Tuple3<ZonedDateTime, String, Boolean> combineOuijaBoardDetails(
            List<DataForDashboardVO> ouijaBoards) {

        // 检查所有CTComms是否为true
        boolean allCTCommsTrue = Optional.ofNullable(ouijaBoards)
                .orElse(List.of())
                .stream()
                .allMatch(o -> o != null && Boolean.TRUE.equals(o.getCTComms()));

        // 获取最早的WindowsTime
        ZonedDateTime windowsTime = Optional.ofNullable(ouijaBoards)
                .orElse(List.of())
                .stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getWindowsTime)
                .filter(Objects::nonNull)
                .min(ZonedDateTime::compareTo).orElse(null);

        // 获取第一个TimeZone
        String timeZone = Optional.ofNullable(ouijaBoards)
                .orElse(List.of())
                .stream()
                .filter(Objects::nonNull)
                .map(DataForDashboardVO::getTimeZone)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);

        return new Tuple3(windowsTime, timeZone, allCTCommsTrue);
    }

    // 合并AC负载状态
    public static Tuple2<Watt, BigDecimal> combineACLoadStatuses(
            Watt inverterP,
            Watt thirdPartyInverterP,
            Watt siteGridP,
            Watt siteBackupP) {

        // 获取各个Watt对象的BigDecimal值，空值则使用BigDecimal.ZERO
        BigDecimal inverterValue = Watt.getValueOrZero(inverterP).getValue();
        BigDecimal thirdPartyValue = Watt.getValueOrZero(thirdPartyInverterP).getValue();
        BigDecimal gridValue = Watt.getValueOrZero(siteGridP).getValue();
        BigDecimal backupValue = Watt.getValueOrZero(siteBackupP).getValue();

        // 计算总功率：(inverterP + thirdPartyInverterP) - (siteGridP + siteBackupP)
        BigDecimal totalPower = inverterValue
                .add(thirdPartyValue)
                .subtract(gridValue)
                .subtract(backupValue);

        // 确保功率不小于0（根据需求处理负值）
        if (totalPower.compareTo(BigDecimal.ZERO) < 0) {
            totalPower = BigDecimal.ZERO;
        }

        return new Tuple2(new Watt(totalPower), null);
    }

    public static DailyEnergyTotalsVO combineDayTotalsOnSamePhase(List<AllAboutDevice> siteDevices) {
        // 找到协调器设备
        AllAboutDevice coordinator = siteDevices.stream()
                .filter(d -> d.getPhaseRole() == PhaseRole.Coordinator)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Coordinator device not found"));

        // 提取所有设备的DailyEnergyTotals
        List<DailyEnergyTotalsVO> energyTotals = siteDevices.stream()
                .map(d -> d.getDataForDashboard().getDailyEnergyTotals())
                .filter(Objects::nonNull).collect(Collectors.toList());

        // 计算各项能源总和
        WattHour solarDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarDayTotalE);
        WattHour solarRedbackPvDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarRedbackPvDayTotalE);
        WattHour solarThirdPartyDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarThirdPartyDayTotalE);
        WattHour loadDayTotalAcE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getLoadDayTotalAcE);
        WattHour loadDayTotalBackupE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getLoadDayTotalBackupE);
        WattHour batteryDayTotalImportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getBatteryDayTotalImportE);
        WattHour batteryDayTotalExportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getBatteryDayTotalExportE);

        // 从协调器获取电网数据
        DailyEnergyTotalsVO coordinatorTotals = coordinator.getDataForDashboard().getDailyEnergyTotals();
        WattHour gridDayTotalImportE = coordinatorTotals.getGridDayTotalImportE();
        WattHour gridDayTotalExportE = coordinatorTotals.getGridDayTotalExportE();

        // 构建并返回合并后的能源总计
        return DailyEnergyTotalsVO.builder()
                .solarDayTotalE(solarDayTotalE)
                .solarRedbackPvDayTotalE(solarRedbackPvDayTotalE)
                .solarThirdPartyDayTotalE(solarThirdPartyDayTotalE)
                .loadDayTotalAcE(loadDayTotalAcE)
                .loadDayTotalBackupE(loadDayTotalBackupE)
                .gridDayTotalImportE(gridDayTotalImportE)
                .gridDayTotalExportE(gridDayTotalExportE)
                .batteryDayTotalImportE(batteryDayTotalImportE)
                .batteryDayTotalExportE(batteryDayTotalExportE)
                .build();
    }

    public static DailyEnergyTotalsVO combineDayTotalsOnDifferentPhases(List<DataForDashboardVO> dashboardData) {
        // 提取所有DailyEnergyTotals，过滤null值
        List<DailyEnergyTotalsVO> energyTotals = dashboardData.stream()
                .map(DataForDashboardVO::getDailyEnergyTotals)
                .filter(Objects::nonNull).collect(Collectors.toList());

        // 计算各项能源总和（如果所有值为null，则返回null）
        WattHour solarDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarDayTotalE);
        WattHour solarRedbackPvDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarRedbackPvDayTotalE);
        WattHour solarThirdPartyDayTotalE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getSolarThirdPartyDayTotalE);
        WattHour loadDayTotalAcE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getLoadDayTotalAcE);
        WattHour loadDayTotalBackupE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getLoadDayTotalBackupE);
        WattHour gridDayTotalImportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getGridDayTotalImportE);
        WattHour gridDayTotalExportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getGridDayTotalExportE);
        WattHour batteryDayTotalImportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getBatteryDayTotalImportE);
        WattHour batteryDayTotalExportE = sumIfPresent(energyTotals, DailyEnergyTotalsVO::getBatteryDayTotalExportE);

        // 使用构建器创建合并后的能源总计对象
        return DailyEnergyTotalsVO.builder()
                .solarDayTotalE(solarDayTotalE)
                .solarRedbackPvDayTotalE(solarRedbackPvDayTotalE)
                .solarThirdPartyDayTotalE(solarThirdPartyDayTotalE)
                .loadDayTotalAcE(loadDayTotalAcE)
                .loadDayTotalBackupE(loadDayTotalBackupE)
                .gridDayTotalImportE(gridDayTotalImportE)
                .gridDayTotalExportE(gridDayTotalExportE)
                .batteryDayTotalImportE(batteryDayTotalImportE)
                .batteryDayTotalExportE(batteryDayTotalExportE)
                .build();
    }

    // 辅助方法：安全地计算总和，当所有值为null时返回null
    private static <T> WattHour sumIfPresent(List<T> list, java.util.function.Function<T, WattHour> mapper) {
        Optional<WattHour> sum = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(WattHour::add);
        return sum.orElse(null);
    }

    // 合并电池备用信息
    public static List<BatteryBackUpDto> combineBatteryBackups(List<AllAboutDevice> siteDevices) {
        if (siteDevices == null || siteDevices.isEmpty()) {
            return List.of();
        }

        // 检查是否所有设备都是NoBackup或NoBatteries状态
        boolean allNoBackupOrNoBatteries = siteDevices.stream()
                .allMatch(d -> d.getBatteryBackUp() == null ||
                        d.getBatteryBackUp().getBackUpSupport() == BackUpSupportStatus.NoBackup ||
                        d.getBatteryBackUp().getBackUpSupport() == BackUpSupportStatus.NoBatteries);

        if (allNoBackupOrNoBatteries) {
            // 检查是否有NoBackup状态的设备
            Optional<AllAboutDevice> noBackupDevice = siteDevices.stream()
                    .filter(d -> d.getBatteryBackUp() != null &&
                            d.getBatteryBackUp().getBackUpSupport() == BackUpSupportStatus.NoBackup)
                    .findFirst();

            if (noBackupDevice.isPresent()) {
                BatteryBackUpVO firstBackup = noBackupDevice.get().getBatteryBackUp();
                return List.of(new BatteryBackUpDto(
                        null,
                        BackUpSupportStatus.NoBackup,
                        firstBackup != null && firstBackup.isSupportsConnectedPV()
                ));
            } else {
                // 查找NoBatteries状态的设备
                Optional<AllAboutDevice> noBatteriesDevice = siteDevices.stream()
                        .filter(d -> d.getBatteryBackUp() != null &&
                                d.getBatteryBackUp().getBackUpSupport() == BackUpSupportStatus.NoBatteries)
                        .findFirst();

                if (noBatteriesDevice.isPresent()) {
                    BatteryBackUpVO firstBackup = noBatteriesDevice.get().getBatteryBackUp();
                    return List.of(new BatteryBackUpDto(
                            null,
                            BackUpSupportStatus.NoBatteries,
                            firstBackup != null && firstBackup.isSupportsConnectedPV()
                    ));
                }
            }
        }

        // 重要：确保备用列表有序，以便前端在刷新之间可以可靠地保持索引
        return siteDevices.stream()
                .filter(Objects::nonNull)
                .filter(d -> d.getBatteryBackUp() != null &&
                        d.getBatteryBackUp().getBackUpSupport() != BackUpSupportStatus.NoBackup &&
                        d.getBatteryBackUp().getBackUpSupport() != BackUpSupportStatus.NoBatteries)
                .sorted((d1, d2) -> {
                    String sn1 = d1.getSerialNumber();
                    String sn2 = d2.getSerialNumber();
                    if (sn1 == null && sn2 == null) return 0;
                    if (sn1 == null) return 1;
                    if (sn2 == null) return -1;
                    return sn1.compareTo(sn2);
                })
                .map(AllAboutDevice::getBatteryBackUp)
                .filter(Objects::nonNull)
                .map(backUp -> new BatteryBackUpDto(
                        backUp.getHrsBackUp(),
                        backUp.getBackUpSupport(),
                        backUp.isSupportsConnectedPV(),
                        backUp.getSerialNumber()
                ))
                .collect(Collectors.toList());
    }

    // 获取电池禁用日期时间
    public static String getDateTimeDisabled(
            Integer mismatchedTimeMinutes,
            int numberOfConsecutiveDaysBatteriesMismatchedToDisabled,
            String localTimeZone,
            Instant referenceInstant) {

        if (mismatchedTimeMinutes != null && mismatchedTimeMinutes > 0) {
            // 计算已不匹配的持续时间
            Duration mismatchedDuration = Duration.ofMinutes(mismatchedTimeMinutes);

            // 计算直到禁用的总持续时间
            Duration durationTillDisabled = Duration.ofDays(numberOfConsecutiveDaysBatteriesMismatchedToDisabled)
                    .minus(mismatchedDuration);

            // 计算禁用时间（UTC）
            Instant disabledTimeUtc = referenceInstant.plus(durationTillDisabled);

            // 转换时区
            ZoneId timeZone = null;
            if (localTimeZone != null && !localTimeZone.isEmpty()) {
                try {
                    // 尝试使用时区转换工具
                    timeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(localTimeZone);
                } catch (Exception e) {
                    // 如果转换失败，使用系统默认时区
                    timeZone = ZoneId.systemDefault();
                }
            } else {
                timeZone = ZoneId.systemDefault();
            }

            if (timeZone != null) {
                // 转换为本地时区的日期时间
                ZonedDateTime zonedTime = disabledTimeUtc.atZone(timeZone);

                // 格式化为日期字符串（午夜时间）
                LocalDate date = zonedTime.toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d MMMM yyyy");
                return date.format(formatter);
            }
        }

        return "";
    }

    // 合并电池即将禁用信息
    public static List<BatteryDisableDto> combineBatteryUpcomingDisablements(
            List<AllAboutDevice> siteDevices,
            int numberOfConsecutiveDaysBatteriesMismatchedToDisabled) {

        if (siteDevices == null || siteDevices.isEmpty()) {
            return List.of();
        }

        // 重要：确保备用列表有序，以便前端在刷新之间可以可靠地保持索引
        // 已禁用的电池显示在前面，这样可以明显看出哪些已被禁用，或将首先被禁用
        List<BatteryDisableDto> batteriesWillBeDisabled = new ArrayList<>();

        List<AllAboutDevice> sortedDevices = siteDevices.stream()
                .filter(Objects::nonNull)
                .sorted((d1, d2) -> {
                    // 首先按是否已禁用排序（已禁用的排在前面）
                    BatteryStatus battery1 = d1.getDataForDashboard() != null ? d1.getDataForDashboard().getBatterySummary() : null;
                    BatteryStatus battery2 = d2.getDataForDashboard() != null ? d2.getDataForDashboard().getBatterySummary() : null;

                    boolean disabled1 = battery1 != null && battery1.getStatus() == BatteryStatusValue.Disabled;
                    boolean disabled2 = battery2 != null && battery2.getStatus() == BatteryStatusValue.Disabled;

                    if (disabled1 && !disabled2) return -1;
                    if (!disabled1 && disabled2) return 1;

                    // 然后按不匹配时间降序排序
                    Integer mismatch1 = battery1 != null ? battery1.getMismatchedTimeMinutes() : null;
                    Integer mismatch2 = battery2 != null ? battery2.getMismatchedTimeMinutes() : null;

                    if (mismatch1 == null && mismatch2 == null) return 0;
                    if (mismatch1 == null) return 1;
                    if (mismatch2 == null) return -1;
                    int mismatchCompare = Integer.compare(mismatch2, mismatch1); // 降序

                    if (mismatchCompare != 0) return mismatchCompare;

                    // 最后按序列号排序
                    String sn1 = d1.getSerialNumber();
                    String sn2 = d2.getSerialNumber();
                    if (sn1 == null && sn2 == null) return 0;
                    if (sn1 == null) return 1;
                    if (sn2 == null) return -1;
                    return sn1.compareTo(sn2);
                })
                .collect(Collectors.toList());

        for (AllAboutDevice device : sortedDevices) {
            BatteryStatus batterySummary = device.getDataForDashboard() != null
                ? device.getDataForDashboard().getBatterySummary()
                : null;

            if (batterySummary != null &&
                (batterySummary.getStatus() == BatteryStatusValue.Disabled ||
                 (device.getIsBatteryMismatchProtectionEnabled() != null && device.getIsBatteryMismatchProtectionEnabled() &&
                  device.getDataForDashboard() != null &&
                  device.getDataForDashboard().getLastStatusDateUtc() != null &&
                  batterySummary.getMismatchedTimeMinutes() != null &&
                  batterySummary.getMismatchedTimeMinutes() != 0))) {

                if (batterySummary.getStatus() == BatteryStatusValue.Disabled) {
                    BatteryDisableDto upcomingDisablement = new BatteryDisableDto(
                            device.getSerialNumber(),
                            batterySummary.getStatus().toString(),
                            "");
                    batteriesWillBeDisabled.add(upcomingDisablement);
                } else {
                    String dateBatteriedWillBeDisabled = getDateTimeDisabled(
                            device.getIsBatteryMismatchProtectionEnabled() != null && device.getIsBatteryMismatchProtectionEnabled()
                                ? batterySummary.getMismatchedTimeMinutes()
                                : null,
                            numberOfConsecutiveDaysBatteriesMismatchedToDisabled,
                            device.getDataForDashboard().getTimeZone(),
                            device.getDataForDashboard().getLastStatusDateUtc().toInstant());

                    BatteryDisableDto upcomingDisablement = new BatteryDisableDto(
                            device.getSerialNumber(),
                            batterySummary.getStatus().toString(),
                            dateBatteriedWillBeDisabled);
                    batteriesWillBeDisabled.add(upcomingDisablement);
                }
            }
        }

        return batteriesWillBeDisabled;
    }

    // 处理站点电池状态
    public static BatteryStatusDto processSiteBatteryStatus(
            AllAboutSiteOverview allAboutSiteOverview,
            EnergyFlowDto siteFlow,
            int numberOfConsecutiveDaysBatteriesMismatchedToDisabled) {

        // 合并电池备用信息
        List<BatteryBackUpDto> batteryBackUps = combineBatteryBackups(allAboutSiteOverview.getDeviceDetails());

        // 合并电池即将禁用信息
        List<BatteryDisableDto> batteryUpcomingDisablements = combineBatteryUpcomingDisablements(
                allAboutSiteOverview.getDeviceDetails(),
                numberOfConsecutiveDaysBatteriesMismatchedToDisabled);

        // 计算SoC (0-1范围)
        BigDecimal soC0to1 = null;
        if (allAboutSiteOverview.getDataForDashboard() != null
            && allAboutSiteOverview.getDataForDashboard().getBatterySummary() != null
            && allAboutSiteOverview.getDataForDashboard().getBatterySummary().getSoC() != null) {

            Double soc = allAboutSiteOverview.getDataForDashboard().getBatterySummary().getSoC();
            soC0to1 = BigDecimal.valueOf(soc / 100.0);
        }

        // 确定电池状态
        String state = "No Battery";
        if (siteFlow.isHasBatteries() && siteFlow.getBatteryStatus() != null) {
            state = siteFlow.getBatteryStatus();
        }

        // 获取BMS版本不匹配状态
        boolean bmsVersionMismatch = false;
        if (allAboutSiteOverview.getDataForDashboard() != null) {
            bmsVersionMismatch = allAboutSiteOverview.getDataForDashboard().isBmsVersionMismatch();
        }

        return new BatteryStatusDto(
                allAboutSiteOverview.getPublicSiteId(),
                siteFlow.isOnline(),
                siteFlow.getDateLastStatusReceivedUtc(),
                soC0to1,
                state,
                batteryBackUps,
                siteFlow.isHasBatteries(),
                batteryUpcomingDisablements,
                bmsVersionMismatch);
    }

}
