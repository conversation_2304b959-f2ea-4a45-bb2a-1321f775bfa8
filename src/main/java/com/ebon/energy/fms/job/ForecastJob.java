package com.ebon.energy.fms.job;

import com.ebon.energy.fms.domain.entity.ProductDailyForecastDO;
import com.ebon.energy.fms.service.ForecastService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ForecastJob {

    @Resource
    private ForecastService forecastService;

    /**
     * 逆变器电量预测
     */
    @Scheduled(fixedDelay = 600 * 1000)
    public void processForecast() {
        log.info("开始执行逆变器电量预测任务...");

        List<String> inverterSns = forecastService.getInverterSns();
        for (String inverterSn : inverterSns) {
            try {
                List<ProductDailyForecastDO> list = new ArrayList<>();
                Map<String, BigDecimal> dailyGeneration;
                try {
                    dailyGeneration = forecastService.getDailyGeneration(inverterSn);
                } catch (Exception e) {
                    try {
                        dailyGeneration = forecastService.getDailyGeneration(inverterSn);
                    } catch (Exception ex) {
                        continue;
                    }
                }

                for (Map.Entry<String, BigDecimal> entry : dailyGeneration.entrySet()) {
                    String date = entry.getKey();
                    BigDecimal generation = entry.getValue();

                    ProductDailyForecastDO forecastDO = new ProductDailyForecastDO();
                    forecastDO.setRedbackProductSn(inverterSn);
                    forecastDO.setDate(date);
                    forecastDO.setGeneration(generation);
                    list.add(forecastDO);
                }

                forecastService.saveBatch(list);
            } catch (Exception e) {
                log.error("forecast job error, sn: {}", inverterSn, e);
            }
        }

        log.info("逆变器电量预测任务完成，共处理 {} 个逆变器数据", inverterSns.size());
    }
    
}