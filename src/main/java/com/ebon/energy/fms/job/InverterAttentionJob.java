package com.ebon.energy.fms.job;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.entity.ErrorMappingDO;
import com.ebon.energy.fms.domain.entity.InverterAttentionDO;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.entity.RedbackProductsDO;
import com.ebon.energy.fms.domain.vo.telemetry.Error;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.MetricsTelemetryMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.service.InverterAttentionService;
import com.ebon.energy.fms.service.InverterService;
import com.ebon.energy.fms.service.TelemetryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.utils.ProductUtilities.isOnline;

@Service
@Slf4j
public class InverterAttentionJob {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private MetricsTelemetryMapper metricsTelemetryMapper;

    @Resource
    private InverterAttentionService inverterAttentionService;

    @Resource
    private InverterService inverterService;

    @Resource
    private TelemetryService telemetryService;

    // 每页大小
    private static final int PAGE_SIZE = 2000;

    /**
     * 处理所有存在常规错误 + 需要关注的逆变器
     */
    @Scheduled(fixedDelay = 60 * 1000)
    public void processAttention() {
        log.info("开始执行逆变器关注任务...");

        int currentPage = 1;
        boolean hasNext = true;
        long totalProcessed = 0;

        List<InverterAttentionDO> metricsList = metricsTelemetryMapper.selectAttention();
        Map<String, InverterAttentionDO> metricsMap = metricsList.stream().collect(Collectors.toMap(InverterAttentionDO::getRedbackProductSn, Function.identity(), (first, second) -> first));

        Map<Integer, ErrorMappingDO> allErrorMap = telemetryService.getAllErrorMap();

        while (hasNext) {
            try {
                // 创建分页对象
                Page<RedbackProductsDO> page = new Page<>(currentPage, PAGE_SIZE);

                // 执行分页查询
                IPage<RedbackProductsDO> result = productMapper.selectPage(page, null);

                // 处理当前页数据
                List<RedbackProductsDO> records = result.getRecords();
                processBatch(records, metricsMap, allErrorMap);

                // 更新统计信息
                totalProcessed += records.size();
                //log.info("InverterAttentionJob 已处理第 {} 页数据，本页 {} 条，累计 {} 条", currentPage, records.size(), totalProcessed);

                // 检查是否还有下一页
                hasNext = result.getCurrent() < result.getPages();
                currentPage++;

                // 防止内存溢出，适当间隔
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("InverterAttentionJob 处理第 {} 页数据时出错: {}", currentPage, e.getMessage(), e);
                break;
            }
        }

        log.info("逆变器关注任务完成，共处理 {} 条数据", totalProcessed);
    }

    /**
     * 处理每批数据
     */
    private void processBatch(List<RedbackProductsDO> products, Map<String, InverterAttentionDO> metricsMap, Map<Integer, ErrorMappingDO> allErrorMap) {
        List<InverterAttentionDO> attentions = inverterAttentionService.getByIds(products.stream().map(RedbackProductsDO::getRedbackProductSn).collect(Collectors.toList()));
        Map<String, InverterAttentionDO> attentionMap = attentions.stream().collect(Collectors.toMap(InverterAttentionDO::getRedbackProductSn, Function.identity()));

        List<InvertersDO> updInverters = new ArrayList<>();
        for (RedbackProductsDO productDO : products) {
            try {
                InverterAttentionDO metricsDO = metricsMap.get(productDO.getRedbackProductSn());
                InverterAttentionDO dbAttentionDO = attentionMap.get(productDO.getRedbackProductSn());

                List<Error> errors = null;
                if (StringUtils.isNotBlank(productDO.getLatestSystemStatus())) {
                    SystemStatus systemStatus = JSONObject.parseObject(productDO.getLatestSystemStatus(), SystemStatus.class);

                    InvertersDO invertersDO = new InvertersDO();
                    invertersDO.setSerialNumber(productDO.getRedbackProductSn());
                    invertersDO.setIsHourlyOnline(isOnline(systemStatus.getDate(), 3600000L));
                    invertersDO.setIsDailyOnline(isOnline(systemStatus.getDate(), 86400000L));
                    updInverters.add(invertersDO);

                    errors = systemStatus.getSystemStatusErrors();

                    if (CollectionUtils.isNotEmpty(errors)) {
                        for (Error error : errors) {
                            ErrorMappingDO errorMappingDO = allErrorMap.get(error.getErrorCode());
                            if (errorMappingDO != null) {
                                error.setErrorDescription(errorMappingDO.getDescription());
                            }
                        }
                    }
                }

                if (CollectionUtils.isEmpty(errors)) {
                    if (dbAttentionDO != null) {
                        dbAttentionDO.setStatus(0);
                        dbAttentionDO.setHasError(false);
                        dbAttentionDO.setNeedAttention(false);
                        dbAttentionDO.setIncidentsForRb(0);
                        dbAttentionDO.setIncidentsForInstaller(0);
                        dbAttentionDO.setIncidentsForHomeUser(0);
                        dbAttentionDO.setLastSystemStatusReceived(productDO.getLastSystemStatusReceived());
                        dbAttentionDO.setLastErrors("");
                        inverterAttentionService.update(dbAttentionDO);
                    }

                    continue;
                }

                if (dbAttentionDO != null) {
                    if (StringUtils.isBlank(dbAttentionDO.getLastErrors())) {
                        //原错误信息为空，存在新错误，状态更新为待处理
                        dbAttentionDO.setStatus(0);
                    } else {
                        List<Error> dbErrors = JSONObject.parseArray(dbAttentionDO.getLastErrors(), Error.class);
                        for (Error error : errors) {
                            boolean hasSameError = false;
                            for (Error dbError : dbErrors) {
                                hasSameError = compareTo(dbError, error);
                                if (hasSameError) {
                                    break;
                                }
                            }

                            if (!hasSameError) {
                                //原错误列表中不存在当前错误，状态更新为待处理
                                dbAttentionDO.setStatus(0);
                                break;
                            }
                        }
                    }

                    dbAttentionDO.setHasError(true);
                    dbAttentionDO.setLastSystemStatusReceived(productDO.getLastSystemStatusReceived());
                    dbAttentionDO.setLastErrors(JSONObject.toJSONString(errors));

                    if (metricsDO != null) {
                        dbAttentionDO.setNeedAttention(true);
                        dbAttentionDO.setIncidentsForRb(metricsDO.getIncidentsForRb());
                        dbAttentionDO.setIncidentsForInstaller(metricsDO.getIncidentsForInstaller());
                        dbAttentionDO.setIncidentsForHomeUser(metricsDO.getIncidentsForHomeUser());
                    } else {
                        dbAttentionDO.setNeedAttention(false);
                        dbAttentionDO.setIncidentsForRb(0);
                        dbAttentionDO.setIncidentsForInstaller(0);
                        dbAttentionDO.setIncidentsForHomeUser(0);
                    }

                    inverterAttentionService.update(dbAttentionDO);
                } else {
                    InverterAttentionDO attentionDO = new InverterAttentionDO();
                    attentionDO.setRedbackProductSn(productDO.getRedbackProductSn());
                    attentionDO.setStatus(0);
                    attentionDO.setHasError(true);
                    attentionDO.setLastSystemStatusReceived(productDO.getLastSystemStatusReceived());
                    attentionDO.setLastErrors(JSONObject.toJSONString(errors));

                    if (metricsDO != null) {
                        attentionDO.setNeedAttention(true);
                        attentionDO.setIncidentsForRb(metricsDO.getIncidentsForRb());
                        attentionDO.setIncidentsForInstaller(metricsDO.getIncidentsForInstaller());
                        attentionDO.setIncidentsForHomeUser(metricsDO.getIncidentsForHomeUser());
                    }

                    inverterAttentionService.save(attentionDO);
                }
            } catch (Exception e) {
                log.error("InverterAttentionJob error, sn: {}", productDO.getRedbackProductSn(), e);
            }
        }

        inverterService.updateBatch(updInverters);
    }

    private boolean compareTo(Error dbError, Error error) {
        boolean sameErrorCode = Objects.equals(dbError.getErrorCode(), error.getErrorCode());
        boolean sameFirst = Objects.equals(dbError.getFirstUtc(), error.getFirstUtc());
        boolean sameLatest = Objects.equals(dbError.getLatestUtc(), error.getLatestUtc());
        boolean sameDesc = Objects.equals(dbError.getDescription(), error.getDescription());
        
        return sameErrorCode && sameFirst && sameLatest && sameDesc;
    }

}