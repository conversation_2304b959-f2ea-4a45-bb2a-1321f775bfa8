// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.site;

import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * 产品日缓存模型类
 */
@Data
@NoArgsConstructor
public class ProductDailyCacheModel implements IProductDailyCache {
    
    private LocalDateTime time;
    
    @JsonProperty("SerialNumber")
    private String serialNumber;
    
    @JsonProperty("Date")
    private String date;
    
    @JsonProperty("TotalConsumption")
    private double totalConsumption;
    
    @JsonProperty("TotalExport")
    private double totalExport;
    
    @JsonProperty("TotalImport")
    private double totalImport;
    
    @JsonProperty("TotalGeneration")
    private double totalGeneration;
    
    /**
     * 当天推入电池的总能量（千瓦时）
     * 备注：可为空，因为旧记录可能不可用
     */
    @JsonProperty("BatteryChargedkWh")
    private Double batteryChargedkWh;
    
    /**
     * 当天从电池取出的总能量（千瓦时）
     * 备注：可为空，因为旧记录可能不可用
     */
    @JsonProperty("BatteryDischargedkWh")
    private Double batteryDischargedkWh;
    
    /**
     * 构造函数
     * 
     * @param serialNumber 序列号
     * @param time 时间戳
     * @param totalConsumptionKwh 总消耗千瓦时
     * @param totalExportKwh 总导出千瓦时
     * @param totalImportKwh 总导入千瓦时
     * @param totalGenerationKwh 总发电千瓦时
     * @param batteryChargedkWh 电池充电千瓦时
     * @param batteryDischargedkWh 电池放电千瓦时
     */
    public ProductDailyCacheModel(
            String serialNumber,
            LocalDateTime time,
            double totalConsumptionKwh,
            double totalExportKwh,
            double totalImportKwh,
            double totalGenerationKwh,
            double batteryChargedkWh,
            double batteryDischargedkWh) {
        
        // 对于所有新用途都应提供电池数据，
        // 但数据库可能包含电池数据为null的记录
        // 这就是为什么输入参数是'double'但属性是'Double'的原因。
        
        this.serialNumber = serialNumber;
        this.date = time.format(DateTimeFormatter.ofPattern("dd/MM/yyyy", Locale.US));
        
        // 抛出异常似乎太极端了
        this.time = time;
        this.totalConsumption = totalConsumptionKwh;
        this.totalExport = totalExportKwh;
        this.totalImport = totalImportKwh;
        this.totalGeneration = totalGenerationKwh;
        this.batteryChargedkWh = batteryChargedkWh;
        this.batteryDischargedkWh = batteryDischargedkWh;
    }
    
    /**
     * 记录的时间戳
     * 备注：有一个全局Dapper处理程序使所有检索到的DateTime都是UTC，
     * 这是为一个属性提供自定义行为的笨拙方式。
     */
    @JsonProperty("Time")
    public LocalDateTime getTime() {
        return time;
    }
    
    public void setTime(LocalDateTime time) {
        this.time = time;
    }
}
