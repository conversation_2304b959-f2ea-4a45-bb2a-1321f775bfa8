package com.ebon.energy.fms.domain.vo.site;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BatteryDisableDto {
    
    @JsonProperty("SerialNumber")
    private final String serialNumber;
    
    @JsonProperty("State")
    private final String state;
    
    @JsonProperty("DateBatteriedWillBeDisabled")
    private final String dateBatteriedWillBeDisabled;

    public BatteryDisableDto(
            String serialNumber,
            String state,
            String dateBatteriedWillBeDisabled) {
        this.serialNumber = serialNumber;
        this.state = state;
        this.dateBatteriedWillBeDisabled = dateBatteriedWillBeDisabled;
    }
}
