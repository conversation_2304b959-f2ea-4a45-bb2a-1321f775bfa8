package com.ebon.energy.fms.domain.vo.site;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public class BatteryStatusDto {
    
    @JsonProperty("Id")
    private final String id;
    
    @JsonProperty("IsOnline")
    private final boolean isOnline;
    
    @JsonProperty("DateLastStatusReceivedUtc")
    private final Instant dateLastStatusReceivedUtc;
    
    @JsonProperty("SoC0to1")
    private BigDecimal soC0to1;
    
    @JsonProperty("State")
    private final String state;
    
    @JsonProperty("BatteryBackUp")
    private final BatteryBackUpDto batteryBackUp;
    
    @JsonProperty("BatteryBackUps")
    private final BatteryBackUpDto[] batteryBackUps;
    
    @JsonProperty("HasBatteries")
    private final boolean hasBatteries;
    
    @JsonProperty("DateBatteriedWillBeDisabled")
    private final String dateBatteriedWillBeDisabled;
    
    @JsonProperty("BatteriesWillBeDisabledDetails")
    private final BatteryDisableDto[] batteriesWillBeDisabledDetails;
    
    @JsonProperty("BmsVersionMismatch")
    private final boolean bmsVersionMismatch;

    // 第一个构造函数
    public BatteryStatusDto(
            String id,
            boolean isOnline,
            Instant dateLastStatusReceivedUtc,
            BigDecimal soC0to1,
            String state,
            BatteryBackUpDto batteryBackUp,
            boolean hasBatteries,
            String dateBatteriedWillBeDisabled,
            boolean bmsVersionMismatch) {
        
        this.id = Objects.requireNonNull(id, "id不能为空");
        this.isOnline = isOnline;
        this.dateLastStatusReceivedUtc = dateLastStatusReceivedUtc;
        this.soC0to1 = soC0to1;
        this.state = Objects.requireNonNull(state, "state不能为空");
        this.hasBatteries = hasBatteries;
        this.bmsVersionMismatch = bmsVersionMismatch;

        this.batteryBackUp = batteryBackUp;
        this.batteryBackUps = batteryBackUp != null 
            ? new BatteryBackUpDto[]{batteryBackUp} 
            : null;

        this.dateBatteriedWillBeDisabled = dateBatteriedWillBeDisabled != null 
            ? dateBatteriedWillBeDisabled 
            : "";
        
        this.batteriesWillBeDisabledDetails = 
            (!isNullOrEmpty(dateBatteriedWillBeDisabled) 
                || "DISABLED".equals(state))
            ? new BatteryDisableDto[]{
                new BatteryDisableDto("", state, dateBatteriedWillBeDisabled)
            }
            : null;
    }

    // 第二个构造函数
    public BatteryStatusDto(
            String id,
            boolean isOnline,
            Instant dateLastStatusReceivedUtc,
            BigDecimal soC0to1,
            String state,
            List<BatteryBackUpDto> batteryBackUps,
            boolean hasBatteries,
            List<BatteryDisableDto> batteriesWillBeDisabled,
            boolean bmsVersionMismatch) {
        
        this.id = Objects.requireNonNull(id, "id不能为空");
        this.isOnline = isOnline;
        this.dateLastStatusReceivedUtc = dateLastStatusReceivedUtc;
        this.soC0to1 = soC0to1;
        this.state = Objects.requireNonNull(state, "state不能为空");
        this.hasBatteries = hasBatteries;
        this.bmsVersionMismatch = bmsVersionMismatch;

        this.batteryBackUp = (batteryBackUps != null && !batteryBackUps.isEmpty()) 
            ? batteryBackUps.get(0) 
            : null;
        
        this.batteryBackUps = (batteryBackUps != null && !batteryBackUps.isEmpty())
            ? batteryBackUps.toArray(new BatteryBackUpDto[0])
            : null;

        this.dateBatteriedWillBeDisabled = (batteriesWillBeDisabled != null && !batteriesWillBeDisabled.isEmpty())
            ? (batteriesWillBeDisabled.get(0).getDateBatteriedWillBeDisabled() != null 
                ? batteriesWillBeDisabled.get(0).getDateBatteriedWillBeDisabled() 
                : "")
            : "";
        
        this.batteriesWillBeDisabledDetails = (batteriesWillBeDisabled != null && !batteriesWillBeDisabled.isEmpty())
            ? batteriesWillBeDisabled.toArray(new BatteryDisableDto[0])
            : null;
    }

    // 默认构造函数（带默认值）
    public BatteryStatusDto(
            String id,
            boolean isOnline,
            Instant dateLastStatusReceivedUtc,
            BigDecimal soC0to1,
            String state,
            BatteryBackUpDto batteryBackUp,
            boolean hasBatteries,
            String dateBatteriedWillBeDisabled) {
        this(id, isOnline, dateLastStatusReceivedUtc, soC0to1, state, 
             batteryBackUp, hasBatteries, dateBatteriedWillBeDisabled, false);
    }

    public BatteryStatusDto(
            String id,
            boolean isOnline,
            Instant dateLastStatusReceivedUtc,
            BigDecimal soC0to1,
            String state,
            List<BatteryBackUpDto> batteryBackUps,
            boolean hasBatteries,
            List<BatteryDisableDto> batteriesWillBeDisabled) {
        this(id, isOnline, dateLastStatusReceivedUtc, soC0to1, state,
             batteryBackUps, hasBatteries, batteriesWillBeDisabled, false);
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}


