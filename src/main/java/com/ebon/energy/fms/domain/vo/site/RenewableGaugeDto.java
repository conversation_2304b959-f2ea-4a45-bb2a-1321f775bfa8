// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.site;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor; // 如果需要无参构造函数，但原C#没有，这里不加
import lombok.AllArgsConstructor; // 如果需要全参构造函数，但原C#有两个特定构造函数，这里不加
import lombok.Getter; // 显式声明getter，配合final字段，@Data也会生成getter
import lombok.Setter; // @Data会生成setter，但对于final字段不会生成

import java.util.Objects;

// 使用@Data注解简化getter, equals, hashCode, toString方法
// 对于final字段，@Data不会生成setter
@Data
public class RenewableGaugeDto {

    @JsonProperty("Id")
    private final String id;

    @JsonProperty("PercentageRenewableToday")
    private final Integer percentageRenewableToday;

    @JsonProperty("PercentageRenewableSevenDays")
    private final Integer percentageRenewableSevenDays;

    @JsonProperty("PercentageRenewableThirtyDays")
    private final Integer percentageRenewableThirtyDays;

    @JsonProperty("PercentageRenewableNinetyDays")
    private final Integer percentageRenewableNinetyDays;

    // 对应C#的第一个构造函数
    public RenewableGaugeDto(
            String id,
            Integer percentageRenewableToday,
            Integer percentageRenewableSevenDays,
            Integer percentageRenewableThirtyDays,
            Integer percentageRenewableNinetyDays) {
        this.id = Objects.requireNonNull(id, "id cannot be null"); // 对应C#的 null check
        this.percentageRenewableToday = percentageRenewableToday;
        this.percentageRenewableSevenDays = percentageRenewableSevenDays;
        this.percentageRenewableThirtyDays = percentageRenewableThirtyDays;
        this.percentageRenewableNinetyDays = percentageRenewableNinetyDays;
    }

    // 对应C#的第二个构造函数
    public RenewableGaugeDto(
            String id,
            Integer percentageRenewableSevenDays,
            Integer percentageRenewableThirtyDays,
            Integer percentageRenewableNinetyDays) {
        this.id = Objects.requireNonNull(id, "id cannot be null"); // 对应C#的 null check
        this.percentageRenewableToday = null; // 对应C#的 null 赋值
        this.percentageRenewableSevenDays = percentageRenewableSevenDays;
        this.percentageRenewableThirtyDays = percentageRenewableThirtyDays;
        this.percentageRenewableNinetyDays = percentageRenewableNinetyDays;
    }

    // 对应C#的静态工厂方法
    public static RenewableGaugeDto emptyWith(String id) {
        // C# default(int?) 是 null
        return new RenewableGaugeDto(id, null, null, null);
    }

    // @Data注解已经包含了getter方法，无需手动编写

    // @Data注解已经包含了equals, hashCode, toString方法，无需手动编写
}
