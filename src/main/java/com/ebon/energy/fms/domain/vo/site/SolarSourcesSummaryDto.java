// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.site;

import lombok.Data;
import java.util.List;
import java.util.Objects;

@Data
public class SolarSourcesSummaryDto {
    
    private final List<SolarSourceDto> sources;
    
    public SolarSourcesSummaryDto(List<SolarSourceDto> sources) {
        this.sources = Objects.requireNonNull(sources, "sources cannot be null");
    }
}
