// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.site;

import lombok.Data;
import java.util.Objects;

@Data
public class SolarSourceDto {
    
    private final String name;
    private final int watts;
    
    public SolarSourceDto(String name, int watts) {
        this.name = Objects.requireNonNull(name, "name cannot be null");
        this.watts = watts;
    }
}
