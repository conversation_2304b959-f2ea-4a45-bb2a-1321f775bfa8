package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BatteryBackUpDto {
    
    @JsonProperty("HrsBackUp")
    private final Double hrsBackUp;
    
    @JsonProperty("BackUpSupport")
    private final SystemStatusEnum.BackUpSupportStatus backUpSupport;
    
    @JsonProperty("SupportsConnectedPV")
    private final boolean supportsConnectedPV;
    
    @JsonProperty("SerialNumber")
    private final String serialNumber;

    public BatteryBackUpDto(
            Double hrsBackUp,
            SystemStatusEnum.BackUpSupportStatus backUpSupport,
            boolean supportsConnectedPV,
            String serialNumber) {
        this.hrsBackUp = hrsBackUp;
        this.backUpSupport = backUpSupport;
        this.supportsConnectedPV = supportsConnectedPV;
        this.serialNumber = serialNumber != null ? serialNumber : "";
    }

    // 默认构造函数（带默认值）
    public BatteryBackUpDto(
            Double hrsBackUp,
            SystemStatusEnum.BackUpSupportStatus backUpSupport,
            boolean supportsConnectedPV) {
        this(hrsBackUp, backUpSupport, supportsConnectedPV, "");
    }
}
