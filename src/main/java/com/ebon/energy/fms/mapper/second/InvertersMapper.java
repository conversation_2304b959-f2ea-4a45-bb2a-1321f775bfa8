package com.ebon.energy.fms.mapper.second;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface InvertersMapper extends BaseMapper<InvertersDO> {

    @Select("select * from Inverters where ${query}")
    List<InvertersDO> selectInvertersByQuery(@Param("query") String query);

    @Update({"<script>",
            "UPDATE Inverters",
            "SET",
            "  IsHourlyOnline = CASE SerialNumber",
            "    <foreach collection=\"list\" item=\"item\" separator=\"\">",
            "      WHEN #{item.serialNumber} THEN #{item.isHourlyOnline}",
            "    </foreach>",
            "  END,",
            "  IsDailyOnline = CASE SerialNumber",
            "    <foreach collection=\"list\" item=\"item\" separator=\"\">",
            "      WHEN #{item.serialNumber} THEN #{item.isDailyOnline}",
            "    </foreach>",
            "  END",
            "WHERE SerialNumber IN",
            "  <foreach collection=\"list\" item=\"item\" open=\"(\" separator=\",\" close=\")\">",
            "    #{item.serialNumber}",
            "  </foreach>",
            "</script>"})
    int updateBatch(@Param("list") List<InvertersDO> list);
}
