package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.mapper.third.SiteMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.zone.ZoneRulesException;

@Service
@RequiredArgsConstructor
public class SiteRepository {

    private final SiteMapper siteMapper;

    public LocalDate getBclTimeZoneIdForSite(String publicSiteId) {
        var bclTimeZoneId = siteMapper.getBclTimeZoneIdForSite(publicSiteId);

        if (bclTimeZoneId == null) {
            throw new IllegalArgumentException("No time zone configured for given site");
        }

        var iana = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(bclTimeZoneId);

    }



}
