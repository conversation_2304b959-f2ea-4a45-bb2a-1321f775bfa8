package com.ebon.energy.fms.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

@Slf4j
public class HttpClientUtil {

    public static String get(String url) {
        String apiUrl = url;

        // 创建 HttpClient 实例
        HttpClient client = HttpClient.newHttpClient();

        // 创建 HttpRequest 实例
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl))
                .GET()
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            // 检查响应状态码
            if (response.statusCode() == 200) {
                // 获取响应的 JSON 字符串
                String jsonResponse = response.body();
                //log.info("HttpClientUtil.get Response: " + jsonResponse);
                return jsonResponse;
            } else {
                log.error("HttpClientUtil.get failed with status code: " + response.statusCode());
            }
        } catch (IOException | InterruptedException e) {
            log.error("HttpClientUtil.get error, url:{}", url, e);
        }

        return null;
    }

    public static String get(String url, String cookie) {
        String apiUrl = url;

        // 创建 HttpClient 实例
        HttpClient client = HttpClient.newHttpClient();

        // 创建 HttpRequest 实例
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl))
                .header("Cookie", cookie)
                .GET()
                .build();

        try {
            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态码
            if (response.statusCode() == 200) {
                // 获取响应的 JSON 字符串
                String jsonResponse = response.body();
                //System.out.println("JSON Response: " + jsonResponse);
                return jsonResponse;
            } else {
                System.out.println("Request failed with status code: " + response.statusCode());
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        return null;
    }
}
