spring:
  datasource:
    primary:
      jdbc-url: ${db.fms.url}
      username: ${db.fms.username}
      password: ${db.fms.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    second:
      jdbc-url: ${db.fleetmonitor.url}
      username: ${db.fleetmonitor.username}
      password: ${db.fleetmonitor.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    third:
      jdbc-url: ${db.product.url}
      username: ${db.product.username}
      password: ${db.product.password}
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

authorize:
  jwt:
    secret: "SP&v0P^jV3^b22Fg@DkY*8"
    expire-seconds: 86400

redback:
  baseUrl: https://api1.redbacktech.com
  environment: Production
  serviceAccountName: <EMAIL>
  serviceAccountPassword: ${redback.serviceAccountPassword}

tuya:
  accessKey: ${tuya.accessKey}
  secretKey: ${tuya.secretKey}
  hostName: ${tuya.hostName}

azure:
  iot:
    hub:
      connectionString: ${azure.iot.hub.connectionString}
      hostName: RBProductionIOTHub.azure-devices.net
  storage:
    oldAccountName: rbproductionstorage
    oldAccountKey: ${azure.storage.oldAccountKey}
    telemetryAccountName: rbprodtelemetry
    telemetryAccountKey: ${azure.storage.telemetryAccountKey}
    enableNewTelemetryStorage: false
    useOnlyNewTelemetryFromDate: ********
    useOnlyOldStoragePriorToDate: ********

aliyun:
  tablestore:
    endPoint: ${aliyun.tablestore.endPoint}
    accessKey: ${aliyun.tablestore.accessKey}
    secretKey: ${aliyun.tablestore.secretKey}
    instanceName: tuya-prod
    telemetryStorageFromDateyyyyMMdd: ********
    telemetryOldStoragePriorToDateyyyyMMdd: ********

EMSStorageContainerURL: https://rbprodstorage.blob.core.windows.net/emsfirmwares/

directMethodChangeHostnamePasswords: ${directMethodChangeHostnamePasswords}

portal:
  url: https://portal.redbacktech.com
  redbackBatterySwitch: false
  exportLimitMessageThresholdW: 200
  numberOfConsecutiveDaysBatteriesMismatchedToDisabled: 14
forecast:
  url: http://redback-forecast.tuyaprod:8000