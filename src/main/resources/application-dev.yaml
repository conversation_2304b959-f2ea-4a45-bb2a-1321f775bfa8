spring:
  datasource:
    primary:
      jdbc-url: ************************************************************************************************************
      username: redbactech
      password: 288_ajjTcd8W9**
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    second:
      jdbc-url: ******************************************************************************************************************************
      username: redbactech
      password: 288_ajjTcd8W9**
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    third:
      jdbc-url: *********************************************************************************************************************************
      username: redbactech
      password: 288_ajjTcd8W9**
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

authorize:
  jwt:
    secret: "SP&v0P^jV3^b22Fg@DkY*8"
    expire-seconds: 864000

redback:
  baseUrl: https://rbdevwebapi-leia.azurewebsites.net
  environment: Dev
  serviceAccountName: <EMAIL>
  serviceAccountPassword: Redback@123

tuya:
  accessKey: BAdGK34sQ6yRuRt5XL5z0KTM
  secretKey: Ii9QrrfyiGUxwGsYwAh46tYbuXL78Kz3nrCCoNXlXniW1ECv
  hostName: https://si-c6cfcfa11c49456aab37.tuyacloud.com:8686

azure:
  iot:
    hub:
      connectionString: "HostName=rbtestiothub-leia.azure-devices.net;SharedAccessKeyName=iothubowner;SharedAccessKey=1Pg7hAYrKY9tCkl+k8+adylHxfp6c5MwWyr+7cbdsWs="
      hostName: "rbtestiothub-leia.azure-devices.net"
  storage:
    oldAccountName: ebonexstoragerbdev3
    oldAccountKey: ****************************************************************************************
    telemetryAccountName: rbdev3storage
    telemetryAccountKey: ****************************************************************************************
    enableNewTelemetryStorage: false
    useOnlyNewTelemetryFromDate: "********" # 仅从2023年1月1日起使用新存储
    useOnlyOldStoragePriorToDate: "********" # 仅2022年12月31日之前使用旧存储


aliyun:
  tablestore:
    endPoint: https://redback-dev.cn-hangzhou.ots.aliyuncs.com
    accessKey: LTAI5t8bo1AzeneVQgJkVsau
    secretKey: ******************************
    instanceName: redback-dev
    telemetryStorageFromDateyyyyMMdd: ********
    telemetryOldStoragePriorToDateyyyyMMdd: ********

EMSStorageContainerURL: https://rbteststorageleia.blob.core.windows.net/emsfirmwares/
  
directMethodChangeHostnamePasswords: pmY7SeX18KOmaYztkiA-ZnBhYitCGktI,redbacksnewpassword

portal:
  url: https://webportaldev3.azurewebsites.net
  redbackBatterySwitch: true
  exportLimitMessageThresholdW: 200
  
forecast:
  url: http://redback-forecast.redback.svc.ebonex-newtest:8000